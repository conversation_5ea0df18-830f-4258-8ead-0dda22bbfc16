#!/usr/bin/env python3
"""
تشغيل سريع لنظام الأمان الكمومي المتسامي
Quick Launch for Transcendent Quantum Security System
"""

import sys
import os
import argparse
import webbrowser
import time
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent))

try:
    from quantum_security_app import quantum_system, app, socketio, main
except ImportError as e:
    print(f"❌ خطأ في استيراد التطبيق: {e}")
    print("💡 تأكد من تشغيل: python setup.py")
    sys.exit(1)

def parse_arguments():
    """تحليل معاملات سطر الأوامر"""
    parser = argparse.ArgumentParser(
        description="نظام الأمان الكمومي المتسامي",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python run.py                    # تشغيل عادي
  python run.py --port 8080        # تشغيل على منفذ مخصص
  python run.py --debug            # تشغيل في وضع التطوير
  python run.py --no-browser       # تشغيل بدون فتح المتصفح
  python run.py --host 0.0.0.0     # السماح بالاتصالات الخارجية
        """
    )
    
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="عنوان IP للخادم (افتراضي: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=5000,
        help="منفذ الخادم (افتراضي: 5000)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="تشغيل في وضع التطوير"
    )
    
    parser.add_argument(
        "--no-browser",
        action="store_true",
        help="عدم فتح المتصفح تلقائياً"
    )
    
    parser.add_argument(
        "--auto-start",
        action="store_true",
        help="بدء المراقبة تلقائياً"
    )
    
    return parser.parse_args()

def check_port_available(host, port):
    """فحص توفر المنفذ"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind((host, port))
        return True
    except OSError:
        return False

def open_browser(host, port, delay=2):
    """فتح المتصفح"""
    def _open():
        time.sleep(delay)
        url = f"http://{host}:{port}"
        if host == "0.0.0.0":
            url = f"http://localhost:{port}"
        webbrowser.open(url)
    
    import threading
    threading.Thread(target=_open, daemon=True).start()

def print_banner():
    """طباعة شعار التطبيق"""
    banner = """
    ⚛️  نظام الأمان الكمومي المتسامي  ⚛️
    ═══════════════════════════════════════════
    🛡️  Transcendent Quantum Security System
    🔬  الذكاء الاصطناعي المتقدم
    🔐  التشفير الكمومي الآمن
    🌐  المراقبة المباشرة
    ⚡  الاستجابة الفورية
    ═══════════════════════════════════════════
    """
    print(banner)

def main_cli():
    """الدالة الرئيسية لواجهة سطر الأوامر"""
    args = parse_arguments()
    
    print_banner()
    
    # فحص توفر المنفذ
    if not check_port_available(args.host, args.port):
        print(f"❌ المنفذ {args.port} مستخدم بالفعل")
        print("💡 جرب منفذ آخر باستخدام --port")
        return False
    
    # طباعة معلومات التشغيل
    print(f"🌐 الخادم: {args.host}:{args.port}")
    print(f"🔧 وضع التطوير: {'نعم' if args.debug else 'لا'}")
    print(f"🌍 المتصفح: {'سيفتح تلقائياً' if not args.no_browser else 'لن يفتح'}")
    print(f"⚡ البدء التلقائي: {'نعم' if args.auto_start else 'لا'}")
    print()
    
    # فتح المتصفح إذا لم يتم تعطيله
    if not args.no_browser:
        print("🌍 فتح المتصفح...")
        open_browser(args.host, args.port)
    
    # بدء المراقبة تلقائياً إذا تم تفعيلها
    if args.auto_start:
        print("⚡ بدء المراقبة التلقائية...")
        quantum_system.start_monitoring()
    
    print("🚀 بدء تشغيل الخادم...")
    print("⚠️  اضغط Ctrl+C للإيقاف")
    print("=" * 50)
    
    try:
        # تشغيل الخادم
        socketio.run(
            app,
            host=args.host,
            port=args.port,
            debug=args.debug,
            use_reloader=False  # تعطيل إعادة التحميل التلقائي
        )
    except KeyboardInterrupt:
        print("\n🛑 إيقاف النظام...")
        quantum_system.stop_monitoring()
        print("✅ تم إيقاف النظام بنجاح")
        return True
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        return False

if __name__ == "__main__":
    success = main_cli()
    sys.exit(0 if success else 1)
