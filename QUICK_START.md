# 🚀 تشغيل سريع - نظام الأمان الكمومي المتسامي

## ⚡ التشغيل السريع (أقل من دقيقة)

### 1. تشغيل مباشر
```bash
python quick_start.py
```

### 2. أو تشغيل النسخة المبسطة
```bash
python simple_quantum_security.py
```

### 3. أو تشغيل النسخة الكاملة (إذا كانت المكتبات متوفرة)
```bash
python quantum_security_app.py
```

---

## 🌟 المميزات المتاحة

### ✅ النسخة المبسطة (تعمل فوراً)
- 🛡️ **كشف التهديدات**: خوارزميات ذكية لكشف الهجمات
- 🔐 **التشفير الكمومي**: تشفير آمن مع تدوير المفاتيح
- 📊 **لوحة تحكم تفاعلية**: مراقبة مباشرة للنظام
- 🔍 **مراقبة الشبكة**: تحليل حركة البيانات
- ⚡ **استجابة فورية**: حجب التهديدات تلقائياً

### 🎯 النسخة الكاملة (تتطلب مكتبات إضافية)
- 🧠 **ذكاء اصطناعي متقدم**: نماذج تعلم آلي
- 📈 **تحليل إحصائي**: رسوم بيانية متقدمة
- 🔬 **كشف الشذوذ**: خوارزميات متطورة
- 📊 **تقارير مفصلة**: تحليل شامل للأمان

---

## 🌐 الوصول للنظام

بعد التشغيل، افتح المتصفح على:
```
http://localhost:5000
```

---

## 🔧 استكشاف الأخطاء

### مشكلة: "ModuleNotFoundError"
**الحل:**
```bash
pip install flask flask-socketio cryptography
```

### مشكلة: "Port already in use"
**الحل:** غير المنفذ في الكود أو أوقف التطبيق الآخر

### مشكلة: المتصفح لا يفتح تلقائياً
**الحل:** افتح المتصفح يدوياً على `http://localhost:5000`

---

## 📋 الأوامر المفيدة

### تثبيت المتطلبات يدوياً
```bash
pip install -r requirements.txt
```

### تشغيل الاختبارات
```bash
python test_system.py
```

### توليد بيانات التدريب
```bash
python generate_training_data.py
```

---

## 🎮 كيفية الاستخدام

### 1. بدء المراقبة
- انقر على "تشغيل النظام" في لوحة التحكم
- ستبدأ مراقبة الشبكة تلقائياً

### 2. تشفير البيانات
- أدخل النص في قسم "أدوات التشفير"
- انقر "تشفير كمومي"
- انسخ النص المشفر

### 3. مراقبة التهديدات
- راقب قسم "كشف التهديدات"
- ستظهر التهديدات المكتشفة في الوقت الفعلي
- يتم حجب التهديدات الخطيرة تلقائياً

### 4. عرض السجلات
- راجع "سجل الأحداث الأمنية"
- تصفح التهديدات المكتشفة والمحجوبة

---

## 🔒 الأمان

- ✅ جميع البيانات مشفرة
- ✅ تدوير المفاتيح التلقائي
- ✅ مراقبة مستمرة للتهديدات
- ✅ حجب تلقائي للهجمات
- ✅ سجلات مفصلة لجميع الأنشطة

---

## 🆘 الدعم

### مشاكل شائعة:
1. **النظام لا يبدأ**: تأكد من تثبيت Python 3.7+
2. **خطأ في المكتبات**: استخدم النسخة المبسطة
3. **بطء في الأداء**: أغلق التطبيقات الأخرى

### للمساعدة:
- راجع ملف `README.md` للتفاصيل الكاملة
- شغل `python test_system.py` للتأكد من سلامة النظام

---

**🌟 نظام الأمان الكمومي المتسامي - حماية المستقبل اليوم**
