{"version": 3, "file": "ts-nodes.d.ts", "sourceRoot": "", "sources": ["../../src/ts-estree/ts-nodes.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,EAAE,MAAM,YAAY,CAAC;AAKtC,OAAO,QAAQ,YAAY,CAAC;IAE1B,UAAiB,YAAa,SAAQ,EAAE,CAAC,gBAAgB;KAAG;IAC5D,UAAiB,WAAY,SAAQ,EAAE,CAAC,eAAe;KAAG;IAE1D,UAAiB,mBAAoB,SAAQ,EAAE,CAAC,IAAI;KAAG;IAEvD,UAAiB,iBAAkB,SAAQ,EAAE,CAAC,IAAI;KAAG;IAErD,UAAiB,eAAgB,SAAQ,EAAE,CAAC,IAAI;KAAG;IACnD,UAAiB,gBAAiB,SAAQ,EAAE,CAAC,IAAI;KAAG;CACrD;AAGD,MAAM,MAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;AAE9C,MAAM,MAAM,MAAM,GACd,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,YAAY,GAEf,EAAE,CAAC,YAAY,GAEf,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,KAAK,GACR,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,MAAM,GACT,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,SAAS,GACZ,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,eAAe,GAElB,EAAE,CAAC,2BAA2B,GAC9B,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,6BAA6B,GAChC,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,SAAS,GACZ,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,2BAA2B,GAC9B,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,kBAAkB,GAErB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,yBAAyB,GAC5B,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,KAAK,GACR,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,OAAO,GACV,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,QAAQ,GACX,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,0BAA0B,GAC7B,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,6BAA6B,GAChC,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,0BAA0B,GAC7B,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,sBAAsB,GAEzB,EAAE,CAAC,2BAA2B,GAC9B,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,cAAc,GAGjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,eAAe,CAAC"}