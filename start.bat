@echo off
chcp 65001 >nul
title نظام الأمان الكمومي المتسامي

echo.
echo ⚛️  نظام الأمان الكمومي المتسامي  ⚛️
echo ═══════════════════════════════════════════
echo 🛡️  Transcendent Quantum Security System
echo ═══════════════════════════════════════════
echo.

echo 🔍 فحص متطلبات النظام...

:: فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 💡 يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

:: فحص ملف المتطلبات
if not exist requirements.txt (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo ✅ ملف المتطلبات موجود

:: تثبيت المتطلبات إذا لم تكن مثبتة
echo 📦 فحص وتثبيت المتطلبات...
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ⚠️  تحذير: قد تكون بعض المتطلبات غير مثبتة بشكل صحيح
)

echo ✅ المتطلبات جاهزة

:: إنشاء المجلدات المطلوبة
if not exist templates mkdir templates
if not exist static mkdir static
if not exist logs mkdir logs
if not exist data mkdir data
if not exist models mkdir models

echo ✅ المجلدات جاهزة

:: تشغيل النظام
echo.
echo 🚀 بدء تشغيل النظام...
echo 📊 لوحة التحكم ستفتح على: http://localhost:5000
echo ⚠️  اضغط Ctrl+C لإيقاف النظام
echo.

python run.py --auto-start

echo.
echo 🛑 تم إيقاف النظام
pause
