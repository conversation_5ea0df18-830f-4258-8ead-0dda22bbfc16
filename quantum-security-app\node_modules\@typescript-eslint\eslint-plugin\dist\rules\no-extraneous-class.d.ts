export type Options = [
    {
        allowConstructorOnly?: boolean;
        allowEmpty?: boolean;
        allowStaticOnly?: boolean;
        allowWithDecorator?: boolean;
    }
];
export type MessageIds = 'empty' | 'onlyConstructor' | 'onlyStatic';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-extraneous-class.d.ts.map