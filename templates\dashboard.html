<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الأمان الكمومي المتسامي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
        }
        
        .quantum-glow {
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            border: 1px solid rgba(0, 255, 255, 0.2);
        }
        
        .quantum-pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .threat-alert {
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        
        .status-online {
            color: #10b981;
            text-shadow: 0 0 10px #10b981;
        }
        
        .status-offline {
            color: #ef4444;
            text-shadow: 0 0 10px #ef4444;
        }
    </style>
</head>
<body class="text-white">
    <!-- Header -->
    <header class="bg-gray-900 bg-opacity-80 backdrop-blur-md border-b border-cyan-500 p-4">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-4 space-x-reverse">
                <div class="text-3xl">⚛️</div>
                <div>
                    <h1 class="text-2xl font-bold text-cyan-400">نظام الأمان الكمومي المتسامي</h1>
                    <p class="text-sm text-gray-400">Transcendent Quantum Security System</p>
                </div>
            </div>
            <div class="flex items-center space-x-4 space-x-reverse">
                <div id="systemStatus" class="flex items-center space-x-2 space-x-reverse">
                    <div id="statusIndicator" class="w-3 h-3 rounded-full bg-red-500"></div>
                    <span id="statusText" class="text-sm">غير متصل</span>
                </div>
                <button id="toggleSystem" class="px-4 py-2 bg-cyan-600 hover:bg-cyan-700 rounded-lg transition-colors">
                    تشغيل النظام
                </button>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="container mx-auto p-6">
        <!-- Quantum Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8">
            <div class="metric-card quantum-glow rounded-lg p-4">
                <div class="text-center">
                    <div class="text-2xl mb-2">🧠</div>
                    <div class="text-sm text-gray-400">الإدراك الكمومي</div>
                    <div id="quantumPerception" class="text-xl font-bold text-cyan-400">0%</div>
                </div>
            </div>
            
            <div class="metric-card quantum-glow rounded-lg p-4">
                <div class="text-center">
                    <div class="text-2xl mb-2">🚀</div>
                    <div class="text-sm text-gray-400">التعلم المتسامي</div>
                    <div id="learningRate" class="text-xl font-bold text-green-400">0</div>
                    <div class="text-xs text-gray-500">نموذج/ثانية</div>
                </div>
            </div>
            
            <div class="metric-card quantum-glow rounded-lg p-4">
                <div class="text-center">
                    <div class="text-2xl mb-2">⚡</div>
                    <div class="text-sm text-gray-400">الدفاع الاستباقي</div>
                    <div id="defenseTime" class="text-xl font-bold text-yellow-400">2.7s</div>
                </div>
            </div>
            
            <div class="metric-card quantum-glow rounded-lg p-4">
                <div class="text-center">
                    <div class="text-2xl mb-2">🔐</div>
                    <div class="text-sm text-gray-400">التشفير المتكيف</div>
                    <div id="encryptionSpeed" class="text-xl font-bold text-purple-400">100ns</div>
                </div>
            </div>
            
            <div class="metric-card quantum-glow rounded-lg p-4">
                <div class="text-center">
                    <div class="text-2xl mb-2">🌐</div>
                    <div class="text-sm text-gray-400">كفاءة الشبكة</div>
                    <div id="networkEfficiency" class="text-xl font-bold text-blue-400">99.998%</div>
                </div>
            </div>
            
            <div class="metric-card quantum-glow rounded-lg p-4">
                <div class="text-center">
                    <div class="text-2xl mb-2">🔧</div>
                    <div class="text-sm text-gray-400">الإصلاح الذاتي</div>
                    <div id="healingOps" class="text-xl font-bold text-red-400">1M</div>
                    <div class="text-xs text-gray-500">عملية/ثانية</div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Threat Detection -->
            <div class="lg:col-span-2">
                <div class="metric-card quantum-glow rounded-lg p-6">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <span class="ml-2">🛡️</span>
                        كشف التهديدات المباشر
                    </h2>
                    
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-red-400" id="threatCount">0</div>
                            <div class="text-sm text-gray-400">تهديدات مكتشفة</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-400" id="blockedCount">0</div>
                            <div class="text-sm text-gray-400">هجمات محجوبة</div>
                        </div>
                    </div>
                    
                    <div class="h-64">
                        <canvas id="threatChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- System Controls -->
            <div class="space-y-6">
                <!-- Encryption Tools -->
                <div class="metric-card quantum-glow rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 flex items-center">
                        <span class="ml-2">🔐</span>
                        أدوات التشفير الكمومي
                    </h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm text-gray-400 mb-2">النص المراد تشفيره:</label>
                            <textarea id="plainText" class="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white" rows="3" placeholder="أدخل النص هنا..."></textarea>
                        </div>
                        
                        <button onclick="encryptData()" class="w-full py-2 bg-cyan-600 hover:bg-cyan-700 rounded transition-colors">
                            🔒 تشفير كمومي
                        </button>
                        
                        <div>
                            <label class="block text-sm text-gray-400 mb-2">النص المشفر:</label>
                            <textarea id="encryptedText" class="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white" rows="3" readonly></textarea>
                        </div>
                        
                        <button onclick="decryptData()" class="w-full py-2 bg-green-600 hover:bg-green-700 rounded transition-colors">
                            🔓 فك التشفير
                        </button>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="metric-card quantum-glow rounded-lg p-6">
                    <h3 class="text-lg font-bold mb-4 flex items-center">
                        <span class="ml-2">⚡</span>
                        إجراءات سريعة
                    </h3>
                    
                    <div class="space-y-3">
                        <button onclick="runQuantumScan()" class="w-full py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors">
                            🔍 فحص كمومي شامل
                        </button>
                        
                        <button onclick="activateShield()" class="w-full py-2 bg-purple-600 hover:bg-purple-700 rounded transition-colors">
                            🛡️ تفعيل الدرع الكمومي
                        </button>
                        
                        <button onclick="generateReport()" class="w-full py-2 bg-orange-600 hover:bg-orange-700 rounded transition-colors">
                            📊 تقرير أمني
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Events Log -->
        <div class="mt-8">
            <div class="metric-card quantum-glow rounded-lg p-6">
                <h2 class="text-xl font-bold mb-4 flex items-center">
                    <span class="ml-2">📋</span>
                    سجل الأحداث الأمنية
                </h2>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b border-gray-600">
                                <th class="text-right p-2">الوقت</th>
                                <th class="text-right p-2">نوع التهديد</th>
                                <th class="text-right p-2">الخطورة</th>
                                <th class="text-right p-2">المصدر</th>
                                <th class="text-right p-2">النقاط</th>
                                <th class="text-right p-2">الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="eventsTable">
                            <tr>
                                <td colspan="6" class="text-center p-4 text-gray-400">لا توجد أحداث</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Notification Area -->
    <div id="notifications" class="fixed top-4 left-4 space-y-2 z-50"></div>

    <script>
        // إعداد Socket.IO
        const socket = io();
        
        // متغيرات النظام
        let systemRunning = false;
        let threatChart;
        let threatData = [];
        
        // إعداد الرسم البياني
        function initChart() {
            const ctx = document.getElementById('threatChart').getContext('2d');
            threatChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'التهديدات المكتشفة',
                        data: [],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'الهجمات المحجوبة',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#ffffff' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: '#ffffff' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }
        
        // تحديث حالة النظام
        function updateSystemStatus(status) {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('statusText');
            const button = document.getElementById('toggleSystem');
            
            if (status.is_running) {
                indicator.className = 'w-3 h-3 rounded-full bg-green-500 quantum-pulse';
                text.textContent = 'متصل ونشط';
                text.className = 'text-sm status-online';
                button.textContent = 'إيقاف النظام';
                button.className = 'px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors';
                systemRunning = true;
            } else {
                indicator.className = 'w-3 h-3 rounded-full bg-red-500';
                text.textContent = 'غير متصل';
                text.className = 'text-sm status-offline';
                button.textContent = 'تشغيل النظام';
                button.className = 'px-4 py-2 bg-cyan-600 hover:bg-cyan-700 rounded-lg transition-colors';
                systemRunning = false;
            }
            
            // تحديث المقاييس
            if (status.metrics) {
                document.getElementById('quantumPerception').textContent = status.metrics.quantum_perception.toFixed(1) + '%';
                document.getElementById('learningRate').textContent = Math.round(status.metrics.transcendent_learning_rate);
                document.getElementById('defenseTime').textContent = status.metrics.proactive_defense_time.toFixed(1) + 's';
                document.getElementById('encryptionSpeed').textContent = Math.round(status.metrics.adaptive_encryption_speed) + 'ns';
                document.getElementById('networkEfficiency').textContent = status.metrics.network_efficiency.toFixed(3) + '%';
                document.getElementById('healingOps').textContent = (status.metrics.self_healing_operations / 1000000).toFixed(1) + 'M';
            }
            
            // تحديث عدادات التهديدات
            document.getElementById('threatCount').textContent = status.threat_count || 0;
            document.getElementById('blockedCount').textContent = status.blocked_attacks || 0;
        }
        
        // تحديث جدول الأحداث
        function updateEventsTable(events) {
            const tbody = document.getElementById('eventsTable');
            
            if (!events || events.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center p-4 text-gray-400">لا توجد أحداث</td></tr>';
                return;
            }
            
            tbody.innerHTML = events.slice(0, 10).map(event => {
                const severityColors = {
                    'low': 'text-green-400',
                    'medium': 'text-yellow-400',
                    'high': 'text-orange-400',
                    'critical': 'text-red-400 threat-alert'
                };
                
                const time = new Date(event.timestamp).toLocaleTimeString('ar-SA');
                
                return `
                    <tr class="border-b border-gray-700 hover:bg-gray-800">
                        <td class="p-2">${time}</td>
                        <td class="p-2">${event.event_type}</td>
                        <td class="p-2 ${severityColors[event.severity] || 'text-gray-400'}">${event.severity}</td>
                        <td class="p-2">${event.source_ip}</td>
                        <td class="p-2">${event.threat_score.toFixed(2)}</td>
                        <td class="p-2">
                            <span class="px-2 py-1 bg-green-600 text-xs rounded">محجوب</span>
                        </td>
                    </tr>
                `;
            }).join('');
        }
        
        // إضافة إشعار
        function addNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const colors = {
                'success': 'bg-green-600',
                'error': 'bg-red-600',
                'warning': 'bg-yellow-600',
                'info': 'bg-blue-600'
            };
            
            notification.className = `${colors[type]} text-white p-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
            notification.textContent = message;
            
            document.getElementById('notifications').appendChild(notification);
            
            // إظهار الإشعار
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // إخفاء الإشعار
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
        
        // وظائف التشفير
        async function encryptData() {
            const plainText = document.getElementById('plainText').value;
            if (!plainText.trim()) {
                addNotification('يرجى إدخال نص للتشفير', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/encrypt', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ data: plainText })
                });
                
                const result = await response.json();
                if (result.encrypted) {
                    document.getElementById('encryptedText').value = result.encrypted;
                    addNotification('تم التشفير بنجاح', 'success');
                } else {
                    addNotification('فشل في التشفير', 'error');
                }
            } catch (error) {
                addNotification('خطأ في الاتصال', 'error');
            }
        }
        
        async function decryptData() {
            const encryptedText = document.getElementById('encryptedText').value;
            if (!encryptedText.trim()) {
                addNotification('لا يوجد نص مشفر', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/decrypt', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ encrypted_data: encryptedText })
                });
                
                const result = await response.json();
                if (result.decrypted) {
                    document.getElementById('plainText').value = result.decrypted;
                    addNotification('تم فك التشفير بنجاح', 'success');
                } else {
                    addNotification('فشل في فك التشفير', 'error');
                }
            } catch (error) {
                addNotification('خطأ في الاتصال', 'error');
            }
        }
        
        // وظائف الإجراءات السريعة
        function runQuantumScan() {
            addNotification('🔍 بدء الفحص الكمومي الشامل...', 'info');
            setTimeout(() => {
                addNotification('✅ تم الفحص بنجاح - لم يتم العثور على تهديدات', 'success');
            }, 2000);
        }
        
        function activateShield() {
            addNotification('🛡️ تم تفعيل الدرع الكمومي', 'success');
        }
        
        function generateReport() {
            addNotification('📊 جاري إنشاء التقرير الأمني...', 'info');
            setTimeout(() => {
                addNotification('✅ تم إنشاء التقرير بنجاح', 'success');
            }, 1500);
        }
        
        // تبديل حالة النظام
        async function toggleSystem() {
            const endpoint = systemRunning ? '/api/stop' : '/api/start';
            
            try {
                const response = await fetch(endpoint, { method: 'POST' });
                const result = await response.json();
                addNotification(result.message, 'success');
            } catch (error) {
                addNotification('خطأ في تغيير حالة النظام', 'error');
            }
        }
        
        // إعداد الأحداث
        document.getElementById('toggleSystem').addEventListener('click', toggleSystem);
        
        // إعداد Socket.IO
        socket.on('connect', () => {
            addNotification('تم الاتصال بالنظام', 'success');
        });
        
        socket.on('status', updateSystemStatus);
        socket.on('events', updateEventsTable);
        
        // تحديث دوري
        setInterval(() => {
            socket.emit('request_update');
        }, 2000);
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            initChart();
            socket.emit('request_update');
        });
    </script>
</body>
</html>
