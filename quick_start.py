#!/usr/bin/env python3
"""
تشغيل سريع لنظام الأمان الكمومي المتسامي - النسخة المبسطة
Quick Start for Simplified Quantum Security System
"""

import sys
import subprocess
import webbrowser
import time
import threading

def print_banner():
    """طباعة شعار النظام"""
    print("""
    ⚛️ ═══════════════════════════════════════════ ⚛️
    
         نظام الأمان الكمومي المتسامي
      Transcendent Quantum Security System
                  النسخة المبسطة
    
    🛡️  حماية متقدمة وعملية
    🔐  تشفير آمن
    🧠  كشف التهديدات
    ⚡  استجابة فورية
    
    ⚛️ ═══════════════════════════════════════════ ⚛️
    """)

def install_basic_requirements():
    """تثبيت المتطلبات الأساسية فقط"""
    print("📦 تثبيت المتطلبات الأساسية...")
    
    basic_packages = [
        "flask",
        "flask-socketio", 
        "cryptography"
    ]
    
    for package in basic_packages:
        try:
            print(f"  📥 تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️  فشل في تثبيت {package}")
            print(f"  💡 جرب: pip install {package}")
    
    print("✅ تم تثبيت المتطلبات الأساسية")

def create_basic_directories():
    """إنشاء المجلدات الأساسية"""
    import os
    directories = ["templates", "static", "logs"]
    
    print("📁 إنشاء المجلدات...")
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  ✅ {directory}")

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        webbrowser.open("http://localhost:5000")
        print("🌍 تم فتح المتصفح على http://localhost:5000")
    except:
        print("🌍 افتح المتصفح يدوياً على: http://localhost:5000")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص Python
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        input("اضغط Enter للخروج...")
        return
    
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    
    # إنشاء المجلدات
    create_basic_directories()
    
    # تثبيت المتطلبات
    try:
        install_basic_requirements()
    except Exception as e:
        print(f"⚠️  تحذير: {e}")
        print("💡 قد تحتاج لتثبيت المكتبات يدوياً")
    
    print("\n" + "="*50)
    print("🚀 بدء تشغيل النظام...")
    print("📊 لوحة التحكم: http://localhost:5000")
    print("⚠️  اضغط Ctrl+C للإيقاف")
    print("="*50)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser_delayed, daemon=True)
    browser_thread.start()
    
    # تشغيل النظام
    try:
        from simple_quantum_security import main as run_system
        import asyncio
        asyncio.run(run_system())
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود ملف simple_quantum_security.py")
        input("اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
