#!/usr/bin/env python3
"""
اختبارات نظام الأمان الكمومي المتسامي
Transcendent Quantum Security System Tests
"""

import unittest
import asyncio
import json
import time
import tempfile
import os
from unittest.mock import patch, MagicMock
import numpy as np
import pandas as pd

# استيراد مكونات النظام
from quantum_security_app import (
    QuantumCryptography,
    ThreatDetectionAI,
    QuantumSecurityDatabase,
    QuantumSecuritySystem,
    SecurityEvent,
    QuantumMetrics
)

class TestQuantumCryptography(unittest.TestCase):
    """اختبارات نظام التشفير الكمومي"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.crypto = QuantumCryptography()
    
    def test_encryption_decryption(self):
        """اختبار التشفير وفك التشفير"""
        original_text = "هذا نص سري للاختبار"
        
        # تشفير النص
        encrypted = self.crypto.encrypt_data(original_text)
        self.assertIsNotNone(encrypted)
        self.assertNotEqual(encrypted, original_text)
        
        # فك التشفير
        decrypted = self.crypto.decrypt_data(encrypted)
        self.assertEqual(decrypted, original_text)
    
    def test_key_rotation(self):
        """اختبار تدوير المفاتيح"""
        old_key = self.crypto.master_key
        
        # محاكاة انتهاء وقت المفتاح
        self.crypto.last_rotation = time.time() - 400
        
        # تشفير نص (سيؤدي إلى تدوير المفتاح)
        self.crypto.encrypt_data("test")
        
        # التأكد من تغيير المفتاح
        self.assertNotEqual(old_key, self.crypto.master_key)
    
    def test_empty_data_handling(self):
        """اختبار التعامل مع البيانات الفارغة"""
        encrypted = self.crypto.encrypt_data("")
        self.assertIsNotNone(encrypted)
        
        decrypted = self.crypto.decrypt_data(encrypted)
        self.assertEqual(decrypted, "")

class TestThreatDetectionAI(unittest.TestCase):
    """اختبارات نظام الذكاء الاصطناعي"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.ai = ThreatDetectionAI()
    
    def test_model_training(self):
        """اختبار تدريب النماذج"""
        # إنشاء بيانات تدريب
        training_data = self.ai._generate_synthetic_data(1000)
        
        # تدريب النماذج
        accuracy = self.ai.train_models(training_data)
        
        self.assertTrue(self.ai.is_trained)
        self.assertGreater(accuracy, 0)
        self.assertLessEqual(accuracy, 1)
    
    def test_threat_detection(self):
        """اختبار كشف التهديدات"""
        # تدريب النموذج أولاً
        training_data = self.ai._generate_synthetic_data(1000)
        self.ai.train_models(training_data)
        
        # اختبار بيانات عادية
        normal_data = {
            'packet_size': 1500,
            'connection_duration': 30,
            'bytes_sent': 1000,
            'bytes_received': 2000,
            'failed_logins': 0,
            'port_scans': 0,
            'unusual_hours': 0
        }
        
        is_threat, score, threat_type = self.ai.detect_threat(normal_data)
        self.assertIsInstance(is_threat, bool)
        self.assertIsInstance(score, float)
        self.assertIsInstance(threat_type, str)
        
        # اختبار بيانات مشبوهة
        suspicious_data = {
            'packet_size': 10000,
            'connection_duration': 1,
            'bytes_sent': 100000,
            'bytes_received': 100000,
            'failed_logins': 20,
            'port_scans': 10,
            'unusual_hours': 1
        }
        
        is_threat, score, threat_type = self.ai.detect_threat(suspicious_data)
        # البيانات المشبوهة يجب أن تحصل على نقاط أعلى
        self.assertGreater(score, 0.3)
    
    def test_threat_classification(self):
        """اختبار تصنيف أنواع التهديدات"""
        # تدريب النموذج
        training_data = self.ai._generate_synthetic_data(1000)
        self.ai.train_models(training_data)
        
        # اختبار Port Scanning
        port_scan_data = {'port_scans': 10, 'failed_logins': 0}
        threat_type = self.ai._classify_threat_type(port_scan_data, 0.8)
        self.assertEqual(threat_type, "Port Scanning Attack")
        
        # اختبار Brute Force
        brute_force_data = {'port_scans': 0, 'failed_logins': 15}
        threat_type = self.ai._classify_threat_type(brute_force_data, 0.8)
        self.assertEqual(threat_type, "Brute Force Attack")

class TestQuantumSecurityDatabase(unittest.TestCase):
    """اختبارات قاعدة البيانات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # استخدام قاعدة بيانات مؤقتة
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = QuantumSecurityDatabase(self.temp_db.name)
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        os.unlink(self.temp_db.name)
    
    def test_database_initialization(self):
        """اختبار تهيئة قاعدة البيانات"""
        # التأكد من إنشاء الجداول
        import sqlite3
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['security_events', 'quantum_metrics', 'users']
            for table in expected_tables:
                self.assertIn(table, tables)
    
    def test_log_security_event(self):
        """اختبار تسجيل الأحداث الأمنية"""
        from datetime import datetime
        
        event = SecurityEvent(
            timestamp=datetime.now(),
            event_type="Test Threat",
            severity="high",
            source_ip="*************",
            target="test_system",
            description="اختبار تسجيل حدث أمني",
            threat_score=0.85,
            quantum_signature="test_signature"
        )
        
        # تسجيل الحدث
        self.db.log_security_event(event)
        
        # التحقق من التسجيل
        events = self.db.get_recent_events(1)
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['event_type'], "Test Threat")
        self.assertEqual(events[0]['severity'], "high")
    
    def test_log_quantum_metrics(self):
        """اختبار تسجيل المقاييس الكمومية"""
        metrics = QuantumMetrics(
            quantum_perception=95.5,
            transcendent_learning_rate=1000.0,
            proactive_defense_time=2.7,
            adaptive_encryption_speed=100.0,
            network_efficiency=99.998,
            self_healing_operations=1000000.0
        )
        
        # تسجيل المقاييس
        self.db.log_quantum_metrics(metrics)
        
        # التحقق من التسجيل
        import sqlite3
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM quantum_metrics")
            count = cursor.fetchone()[0]
            self.assertEqual(count, 1)

class TestQuantumSecuritySystem(unittest.TestCase):
    """اختبارات النظام الكامل"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # استخدام قاعدة بيانات مؤقتة
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.system = QuantumSecuritySystem()
        self.system.database = QuantumSecurityDatabase(self.temp_db.name)
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.system.stop_monitoring()
        os.unlink(self.temp_db.name)
    
    @patch('psutil.net_connections')
    @patch('psutil.net_io_counters')
    def test_collect_network_data(self, mock_net_io, mock_connections):
        """اختبار جمع البيانات الشبكية"""
        # محاكاة بيانات الشبكة
        mock_connections.return_value = [MagicMock() for _ in range(5)]
        mock_net_io.return_value = MagicMock(bytes_sent=1000, bytes_recv=2000)
        
        data = self.system._collect_network_data()
        
        self.assertIsInstance(data, dict)
        self.assertIn('packet_size', data)
        self.assertIn('connection_duration', data)
        self.assertIn('active_connections', data)
        self.assertEqual(data['active_connections'], 5)
    
    def test_quantum_signature_generation(self):
        """اختبار توليد التوقيع الكمومي"""
        signature1 = self.system._generate_quantum_signature()
        signature2 = self.system._generate_quantum_signature()
        
        self.assertIsInstance(signature1, str)
        self.assertIsInstance(signature2, str)
        self.assertNotEqual(signature1, signature2)
        self.assertEqual(len(signature1), 16)
    
    def test_system_status(self):
        """اختبار حالة النظام"""
        status = self.system.get_system_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn('is_running', status)
        self.assertIn('metrics', status)
        self.assertIn('threat_count', status)
        self.assertIn('blocked_attacks', status)
    
    def test_encryption_integration(self):
        """اختبار تكامل التشفير"""
        test_data = "بيانات سرية للاختبار"
        
        encrypted = self.system.encrypt_sensitive_data(test_data)
        self.assertIsNotNone(encrypted)
        self.assertNotEqual(encrypted, test_data)
        
        decrypted = self.system.decrypt_sensitive_data(encrypted)
        self.assertEqual(decrypted, test_data)

class TestSystemIntegration(unittest.TestCase):
    """اختبارات التكامل الشامل"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.system = QuantumSecuritySystem()
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.system.stop_monitoring()
    
    async def test_system_initialization(self):
        """اختبار تهيئة النظام الكامل"""
        await self.system.initialize_system()
        
        self.assertTrue(self.system.ai_detector.is_trained)
        self.assertGreater(self.system.metrics.quantum_perception, 0)
        self.assertGreater(self.system.metrics.transcendent_learning_rate, 0)
    
    def test_monitoring_lifecycle(self):
        """اختبار دورة حياة المراقبة"""
        # التأكد من أن النظام غير يعمل في البداية
        self.assertFalse(self.system.is_running)
        
        # بدء المراقبة
        self.system.start_monitoring()
        self.assertTrue(self.system.is_running)
        
        # إيقاف المراقبة
        self.system.stop_monitoring()
        self.assertFalse(self.system.is_running)

def run_performance_tests():
    """اختبارات الأداء"""
    print("🚀 بدء اختبارات الأداء...")
    
    # اختبار سرعة التشفير
    crypto = QuantumCryptography()
    test_data = "بيانات اختبار الأداء" * 100
    
    start_time = time.time()
    for _ in range(100):
        encrypted = crypto.encrypt_data(test_data)
        crypto.decrypt_data(encrypted)
    encryption_time = time.time() - start_time
    
    print(f"⚡ سرعة التشفير: {100/encryption_time:.2f} عملية/ثانية")
    
    # اختبار سرعة كشف التهديدات
    ai = ThreatDetectionAI()
    training_data = ai._generate_synthetic_data(1000)
    ai.train_models(training_data)
    
    test_network_data = {
        'packet_size': 1500,
        'connection_duration': 30,
        'bytes_sent': 1000,
        'bytes_received': 2000,
        'failed_logins': 0,
        'port_scans': 0,
        'unusual_hours': 0
    }
    
    start_time = time.time()
    for _ in range(1000):
        ai.detect_threat(test_network_data)
    detection_time = time.time() - start_time
    
    print(f"🧠 سرعة كشف التهديدات: {1000/detection_time:.2f} فحص/ثانية")

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 نظام الأمان الكمومي المتسامي - اختبارات شاملة")
    print("=" * 60)
    
    # تشغيل اختبارات الوحدة
    print("📋 تشغيل اختبارات الوحدة...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 60)
    
    # تشغيل اختبارات الأداء
    run_performance_tests()
    
    print("\n✅ تم إكمال جميع الاختبارات بنجاح!")

if __name__ == "__main__":
    main()
