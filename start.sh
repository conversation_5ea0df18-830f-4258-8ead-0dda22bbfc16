#!/bin/bash

# نظام الأمان الكمومي المتسامي
# Transcendent Quantum Security System

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# طباعة الشعار
echo -e "${CYAN}"
echo "⚛️  نظام الأمان الكمومي المتسامي  ⚛️"
echo "═══════════════════════════════════════════"
echo "🛡️  Transcendent Quantum Security System"
echo "═══════════════════════════════════════════"
echo -e "${NC}"

# فحص متطلبات النظام
echo -e "${BLUE}🔍 فحص متطلبات النظام...${NC}"

# فحص Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ Python غير مثبت${NC}"
        echo -e "${YELLOW}💡 يرجى تثبيت Python 3.8 أو أحدث${NC}"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ Python متوفر${NC}"

# فحص إصدار Python
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo -e "${RED}❌ إصدار Python قديم: $PYTHON_VERSION${NC}"
    echo -e "${YELLOW}💡 يتطلب Python 3.8 أو أحدث${NC}"
    exit 1
fi

echo -e "${GREEN}✅ إصدار Python مناسب: $PYTHON_VERSION${NC}"

# فحص pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo -e "${RED}❌ pip غير مثبت${NC}"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

echo -e "${GREEN}✅ pip متوفر${NC}"

# فحص ملف المتطلبات
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}❌ ملف requirements.txt غير موجود${NC}"
    exit 1
fi

echo -e "${GREEN}✅ ملف المتطلبات موجود${NC}"

# تثبيت المتطلبات
echo -e "${BLUE}📦 تثبيت المتطلبات...${NC}"
$PIP_CMD install -r requirements.txt > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ تم تثبيت المتطلبات بنجاح${NC}"
else
    echo -e "${YELLOW}⚠️  تحذير: قد تكون بعض المتطلبات غير مثبتة بشكل صحيح${NC}"
fi

# إنشاء المجلدات المطلوبة
echo -e "${BLUE}📁 إنشاء المجلدات...${NC}"
mkdir -p templates static logs data models

echo -e "${GREEN}✅ المجلدات جاهزة${NC}"

# تشغيل النظام
echo ""
echo -e "${PURPLE}🚀 بدء تشغيل النظام...${NC}"
echo -e "${CYAN}📊 لوحة التحكم ستفتح على: http://localhost:5000${NC}"
echo -e "${YELLOW}⚠️  اضغط Ctrl+C لإيقاف النظام${NC}"
echo ""

# إعداد معالج الإشارات لإيقاف النظام بشكل صحيح
trap 'echo -e "\n${RED}🛑 إيقاف النظام...${NC}"; exit 0' INT

# تشغيل التطبيق
$PYTHON_CMD run.py --auto-start

echo -e "${GREEN}✅ تم إيقاف النظام بنجاح${NC}"
