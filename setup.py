#!/usr/bin/env python3
"""
إعداد وتثبيت نظام الأمان الكمومي المتسامي
Setup and Installation for Transcendent Quantum Security System
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "templates",
        "static",
        "logs",
        "data",
        "models"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 تم إنشاء مجلد: {directory}")

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    try:
        # سيتم إنشاء قاعدة البيانات تلقائياً عند تشغيل التطبيق
        print("✅ تم إعداد قاعدة البيانات")
        return True
    except Exception as e:
        print(f"❌ فشل في إعداد قاعدة البيانات: {e}")
        return False

def create_config_file():
    """إنشاء ملف الإعدادات"""
    config_content = """# إعدادات نظام الأمان الكمومي المتسامي
# Transcendent Quantum Security System Configuration

# إعدادات الخادم
HOST=0.0.0.0
PORT=5000
DEBUG=False

# إعدادات قاعدة البيانات
DATABASE_PATH=quantum_security.db

# إعدادات الأمان
SECRET_KEY=your-secret-key-here
ENCRYPTION_KEY_ROTATION_INTERVAL=300

# إعدادات الذكاء الاصطناعي
AI_MODEL_UPDATE_INTERVAL=3600
THREAT_DETECTION_THRESHOLD=0.7
ANOMALY_DETECTION_CONTAMINATION=0.1

# إعدادات المراقبة
MONITORING_INTERVAL=1
LOG_LEVEL=INFO
MAX_LOG_SIZE=10MB

# إعدادات الشبكة
MAX_CONNECTIONS=1000
TIMEOUT=30
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(config_content)
    print("✅ تم إنشاء ملف الإعدادات (.env)")

def main():
    """الدالة الرئيسية للإعداد"""
    print("🌟 إعداد نظام الأمان الكمومي المتسامي")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        return False
    
    # إنشاء المجلدات
    create_directories()
    
    # تثبيت المتطلبات
    if not install_requirements():
        return False
    
    # إعداد قاعدة البيانات
    if not setup_database():
        return False
    
    # إنشاء ملف الإعدادات
    create_config_file()
    
    print("\n🎉 تم إعداد النظام بنجاح!")
    print("\n📋 خطوات التشغيل:")
    print("1. python quantum_security_app.py")
    print("2. افتح المتصفح على: http://localhost:5000")
    print("\n🔧 للمساعدة: python quantum_security_app.py --help")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
