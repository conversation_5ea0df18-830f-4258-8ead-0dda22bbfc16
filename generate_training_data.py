#!/usr/bin/env python3
"""
مولد بيانات التدريب لنظام الأمان الكمومي
Training Data Generator for Quantum Security System
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import random
import hashlib

def generate_network_traffic_data(n_samples=50000):
    """توليد بيانات حركة الشبكة"""
    np.random.seed(42)
    
    # أنواع الأنشطة الشبكية
    activity_types = [
        'web_browsing', 'email', 'file_transfer', 'video_streaming',
        'gaming', 'voip', 'database_query', 'api_call'
    ]
    
    # أنواع التهديدات
    threat_types = [
        'normal', 'dos_attack', 'port_scan', 'brute_force',
        'malware', 'phishing', 'sql_injection', 'xss_attack'
    ]
    
    data = []
    
    for i in range(n_samples):
        # تحديد نوع النشاط
        is_threat = np.random.choice([0, 1], p=[0.85, 0.15])  # 15% تهديدات
        
        if is_threat:
            activity = np.random.choice(threat_types[1:])  # استبعاد 'normal'
            threat_label = 1
        else:
            activity = np.random.choice(activity_types)
            threat_label = 0
        
        # توليد خصائص الشبكة بناءً على نوع النشاط
        if activity == 'dos_attack':
            packet_size = np.random.normal(64, 10)  # حزم صغيرة
            packets_per_second = np.random.normal(10000, 2000)  # معدل عالي
            connection_duration = np.random.exponential(0.1)  # اتصالات قصيرة
            bytes_sent = packet_size * packets_per_second * connection_duration
            bytes_received = np.random.normal(0, 10)  # استقبال قليل
            failed_logins = 0
            port_scans = 0
            
        elif activity == 'port_scan':
            packet_size = np.random.normal(40, 5)  # حزم صغيرة جداً
            packets_per_second = np.random.normal(100, 20)
            connection_duration = np.random.exponential(0.5)
            bytes_sent = packet_size * packets_per_second * connection_duration
            bytes_received = np.random.normal(40, 10)
            failed_logins = 0
            port_scans = np.random.poisson(50)  # فحص منافذ كثيرة
            
        elif activity == 'brute_force':
            packet_size = np.random.normal(200, 50)
            packets_per_second = np.random.normal(10, 3)
            connection_duration = np.random.exponential(30)
            bytes_sent = packet_size * packets_per_second * connection_duration
            bytes_received = np.random.normal(100, 20)
            failed_logins = np.random.poisson(20)  # محاولات دخول فاشلة كثيرة
            port_scans = 0
            
        elif activity == 'web_browsing':
            packet_size = np.random.normal(1500, 300)
            packets_per_second = np.random.normal(5, 2)
            connection_duration = np.random.exponential(60)
            bytes_sent = packet_size * packets_per_second * connection_duration * 0.1
            bytes_received = packet_size * packets_per_second * connection_duration
            failed_logins = np.random.poisson(0.1)
            port_scans = 0
            
        elif activity == 'video_streaming':
            packet_size = np.random.normal(1400, 200)
            packets_per_second = np.random.normal(30, 5)
            connection_duration = np.random.exponential(1800)  # جلسات طويلة
            bytes_sent = packet_size * packets_per_second * connection_duration * 0.05
            bytes_received = packet_size * packets_per_second * connection_duration
            failed_logins = 0
            port_scans = 0
            
        else:  # أنشطة عادية أخرى
            packet_size = np.random.normal(800, 400)
            packets_per_second = np.random.normal(2, 1)
            connection_duration = np.random.exponential(120)
            bytes_sent = packet_size * packets_per_second * connection_duration * 0.3
            bytes_received = packet_size * packets_per_second * connection_duration * 0.7
            failed_logins = np.random.poisson(0.05)
            port_scans = 0
        
        # خصائص إضافية
        timestamp = datetime.now() - timedelta(
            seconds=np.random.randint(0, 86400 * 30)  # آخر 30 يوم
        )
        
        unusual_hours = 1 if timestamp.hour < 6 or timestamp.hour > 22 else 0
        
        # معلومات الشبكة
        source_ip = f"192.168.{np.random.randint(1, 255)}.{np.random.randint(1, 255)}"
        dest_port = np.random.choice([80, 443, 22, 21, 25, 53, 3389, 8080])
        
        # إنشاء السجل
        record = {
            'timestamp': timestamp.isoformat(),
            'source_ip': source_ip,
            'dest_port': dest_port,
            'packet_size': max(0, packet_size),
            'packets_per_second': max(0, packets_per_second),
            'connection_duration': max(0, connection_duration),
            'bytes_sent': max(0, bytes_sent),
            'bytes_received': max(0, bytes_received),
            'failed_logins': max(0, int(failed_logins)),
            'port_scans': max(0, int(port_scans)),
            'unusual_hours': unusual_hours,
            'activity_type': activity,
            'is_threat': threat_label
        }
        
        data.append(record)
    
    return pd.DataFrame(data)

def generate_system_logs(n_samples=10000):
    """توليد سجلات النظام"""
    log_levels = ['INFO', 'WARNING', 'ERROR', 'CRITICAL']
    log_sources = ['system', 'network', 'application', 'security', 'database']
    
    logs = []
    
    for i in range(n_samples):
        timestamp = datetime.now() - timedelta(
            seconds=np.random.randint(0, 86400 * 7)  # آخر 7 أيام
        )
        
        level = np.random.choice(log_levels, p=[0.7, 0.2, 0.08, 0.02])
        source = np.random.choice(log_sources)
        
        # رسائل السجل
        if level == 'CRITICAL':
            messages = [
                'System security breach detected',
                'Database connection failed',
                'Critical service stopped',
                'Memory usage exceeded 95%'
            ]
        elif level == 'ERROR':
            messages = [
                'Authentication failed',
                'File not found',
                'Network timeout',
                'Permission denied'
            ]
        elif level == 'WARNING':
            messages = [
                'High CPU usage detected',
                'Unusual login pattern',
                'Disk space low',
                'Service response slow'
            ]
        else:  # INFO
            messages = [
                'User logged in successfully',
                'Backup completed',
                'Service started',
                'Configuration updated'
            ]
        
        message = np.random.choice(messages)
        
        log_entry = {
            'timestamp': timestamp.isoformat(),
            'level': level,
            'source': source,
            'message': message,
            'user_id': f"user_{np.random.randint(1, 1000)}",
            'session_id': hashlib.md5(f"{timestamp}{i}".encode()).hexdigest()[:8]
        }
        
        logs.append(log_entry)
    
    return pd.DataFrame(logs)

def generate_user_behavior_data(n_samples=20000):
    """توليد بيانات سلوك المستخدمين"""
    user_actions = [
        'login', 'logout', 'file_access', 'file_download', 'file_upload',
        'admin_access', 'config_change', 'user_creation', 'password_change'
    ]
    
    behaviors = []
    
    for i in range(n_samples):
        user_id = f"user_{np.random.randint(1, 500)}"
        action = np.random.choice(user_actions)
        
        timestamp = datetime.now() - timedelta(
            seconds=np.random.randint(0, 86400 * 14)  # آخر 14 يوم
        )
        
        # تحديد ما إذا كان السلوك مشبوهاً
        is_suspicious = 0
        
        # سلوكيات مشبوهة
        if action == 'admin_access' and timestamp.hour < 6:
            is_suspicious = 1  # وصول إداري في ساعات غير عادية
        elif action == 'file_download' and np.random.random() < 0.1:
            is_suspicious = 1  # تحميل ملفات كثيرة
        elif action == 'login' and np.random.random() < 0.05:
            is_suspicious = 1  # محاولات دخول متكررة
        
        behavior = {
            'timestamp': timestamp.isoformat(),
            'user_id': user_id,
            'action': action,
            'ip_address': f"192.168.{np.random.randint(1, 255)}.{np.random.randint(1, 255)}",
            'user_agent': np.random.choice([
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
                'Mozilla/5.0 (X11; Linux x86_64)'
            ]),
            'session_duration': np.random.exponential(1800),  # 30 دقيقة متوسط
            'pages_visited': np.random.poisson(10),
            'files_accessed': np.random.poisson(3),
            'is_suspicious': is_suspicious
        }
        
        behaviors.append(behavior)
    
    return pd.DataFrame(behaviors)

def main():
    """الدالة الرئيسية لتوليد البيانات"""
    print("🔬 توليد بيانات التدريب لنظام الأمان الكمومي")
    print("=" * 50)
    
    # إنشاء مجلد البيانات
    import os
    os.makedirs('data', exist_ok=True)
    
    # توليد بيانات حركة الشبكة
    print("🌐 توليد بيانات حركة الشبكة...")
    network_data = generate_network_traffic_data(50000)
    network_data.to_csv('data/network_traffic.csv', index=False, encoding='utf-8')
    print(f"✅ تم حفظ {len(network_data)} سجل شبكي")
    
    # توليد سجلات النظام
    print("📋 توليد سجلات النظام...")
    system_logs = generate_system_logs(10000)
    system_logs.to_csv('data/system_logs.csv', index=False, encoding='utf-8')
    print(f"✅ تم حفظ {len(system_logs)} سجل نظام")
    
    # توليد بيانات سلوك المستخدمين
    print("👤 توليد بيانات سلوك المستخدمين...")
    user_behavior = generate_user_behavior_data(20000)
    user_behavior.to_csv('data/user_behavior.csv', index=False, encoding='utf-8')
    print(f"✅ تم حفظ {len(user_behavior)} سجل سلوك")
    
    # إنشاء ملف معلومات البيانات
    data_info = {
        'generation_date': datetime.now().isoformat(),
        'datasets': {
            'network_traffic': {
                'file': 'network_traffic.csv',
                'records': len(network_data),
                'threat_percentage': (network_data['is_threat'].sum() / len(network_data)) * 100,
                'description': 'بيانات حركة الشبكة مع تصنيف التهديدات'
            },
            'system_logs': {
                'file': 'system_logs.csv',
                'records': len(system_logs),
                'description': 'سجلات النظام بمستويات مختلفة'
            },
            'user_behavior': {
                'file': 'user_behavior.csv',
                'records': len(user_behavior),
                'suspicious_percentage': (user_behavior['is_suspicious'].sum() / len(user_behavior)) * 100,
                'description': 'بيانات سلوك المستخدمين مع كشف السلوك المشبوه'
            }
        }
    }
    
    with open('data/data_info.json', 'w', encoding='utf-8') as f:
        json.dump(data_info, f, ensure_ascii=False, indent=2)
    
    print("\n📊 ملخص البيانات المولدة:")
    print(f"🌐 حركة الشبكة: {len(network_data):,} سجل ({network_data['is_threat'].sum():,} تهديد)")
    print(f"📋 سجلات النظام: {len(system_logs):,} سجل")
    print(f"👤 سلوك المستخدمين: {len(user_behavior):,} سجل ({user_behavior['is_suspicious'].sum():,} مشبوه)")
    
    print(f"\n✅ تم حفظ جميع البيانات في مجلد 'data'")
    print("🚀 يمكن الآن تشغيل النظام باستخدام هذه البيانات")

if __name__ == "__main__":
    main()
